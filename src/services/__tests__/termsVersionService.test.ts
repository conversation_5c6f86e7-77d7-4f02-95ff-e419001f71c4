import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import TermsVersionService from "../termsVersionService";
import { TermsVersion, TermsVersionDTOInterface } from "../../models/TermsVersion";
import { entitiesConfig } from "@wealthyhood/shared-configs";

describe("TermsVersionService", () => {
  beforeAll(async () => {
    await connectDb("TermsVersionService");
  });
  afterAll(async () => {
    await closeDb();
  });
  afterEach(async () => await clearDb());

  describe("createOrUpdateTermsVersion", () => {
    it("should create a new terms version", async () => {
      const termsVersionData: TermsVersionDTOInterface = {
        termsVersion: "2025.1.1",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK,
        description: "Updated privacy policy"
      };

      const result = await TermsVersionService.createOrUpdateTermsVersion(termsVersionData);

      expect(result.termsVersion).toBe("2025.1.1");
      expect(result.companyEntity).toBe(entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK);
      expect(result.description).toBe("Updated privacy policy");
    });

    it("should update an existing terms revision", async () => {
      const termsVersionData: TermsVersionDTOInterface = {
        termsVersion: "2025.1.1",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK,
        description: "Updated privacy policy"
      };

      // Create first revision
      await TermsVersionService.createOrUpdateTermsVersion(termsVersionData);

      // Update the description
      termsVersionData.description = "Updated privacy policy again";
      const result = await TermsVersionService.createOrUpdateTermsVersion(termsVersionData);

      expect(result.description).toBe("Updated privacy policy again");
    });
  });

  describe("getLatestTermsVersion", () => {
    beforeEach(async () => {
      // Create test data
      const ukTestData = [
        {
          termsVersion: "2025.1.1",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK,
          createdAt: new Date("2025-01-01")
        },
        {
          termsVersion: "2025.2.1",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK,
          createdAt: new Date("2025-02-01")
        },
        {
          termsVersion: "2025.3.1",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK,
          createdAt: new Date("2025-03-01")
        }
      ];

      const euTestData = [
        {
          termsVersion: "2025.5.1",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
          createdAt: new Date("2025-05-01")
        },
        {
          termsVersion: "2025.6.1",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
          createdAt: new Date("2025-06-01")
        },
        {
          termsVersion: "2025.7.1",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
          createdAt: new Date("2025-07-01")
        }
      ];

      await TermsVersion.insertMany([...ukTestData, ...euTestData]);
    });

    it("should return the latest terms version for a company entity", async () => {
      const result = await TermsVersionService.getLatestTermsVersion(
        entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
      );

      expect(result?.termsVersion).toBe("2025.3.1");
      expect(result?.companyEntity).toBe(entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK);

      const euResult = await TermsVersionService.getLatestTermsVersion(
        entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      );

      expect(euResult?.termsVersion).toBe("2025.7.1");
      expect(euResult?.companyEntity).toBe(entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE);
    });
  });

  describe("getCurrentTermsVersion", () => {
    it("should return version from the latest terms version", async () => {
      await TermsVersion.create({
        termsVersion: "2024.1.1",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK,
        createdAt: new Date("2024-01-01")
      });

      await TermsVersion.create({
        termsVersion: "2024.2.1",
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK,
        createdAt: new Date("2024-02-01")
      });

      const result = await TermsVersionService.getCurrentTermsVersion(
        entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
      );

      expect(result).toBe("2024.2.1");
    });

    it("should return fallback version if no terms version document exists", async () => {
      const result = await TermsVersionService.getCurrentTermsVersion(
        entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
      );

      expect(result).toBe("2024.9.1"); // Fallback version
    });
  });
});
