import { hashSHA256 } from "../utils/cryptoUtil";
import logger from "../external-services/loggerService";
import { AddressDocument } from "../models/Address";
import { BadRequestError, InternalServerError } from "../models/ApiErrors";
import {
  UserDataInterface,
  PlatformDataInterface,
  TermsAcceptance,
  TermsAcceptanceDTOInterface
} from "../models/TermsAcceptance";
import { UserDocument, UserPopulationFieldsEnum } from "../models/User";
import DbUtil from "../utils/dbUtil";
import TermsVersionService from "./termsVersionService";

export default class TermsAcceptanceService {
  /**
   * Records a user's acceptance of terms and conditions
   * @param user The user accepting the terms
   * @param platformData The platform data
   * @returns The created terms acceptance record
   */
  public static async recordTermsAcceptance(
    user: UserDocument,
    platformData: PlatformDataInterface
  ): Promise<void> {
    const termsVersion = await TermsVersionService.getCurrentTermsVersion(user.companyEntity);

    await DbUtil.populateIfNotAlreadyPopulated(user, UserPopulationFieldsEnum.ADDRESSES);
    const address = user.addresses[0] as AddressDocument;

    if (!address) {
      logger.error(`User ${user.email} does not have an address, cannot record terms acceptance`, {
        module: "TermsAcceptanceService",
        method: "recordTermsAcceptance",
        data: {
          userId: user._id,
          email: user.email,
          termsVersion
        }
      });
      throw new BadRequestError("User must have an address to accept terms");
    }

    try {
      // Extract personal data from user and address
      const userData: UserDataInterface = {
        fullName: `${user.firstName || ""} ${user.lastName || ""}`.trim(),
        dateOfBirth: user.dateOfBirth,
        address: {
          line1: address.line1,
          line2: address.line2,
          line3: address.line3,
          city: address.city,
          region: address.region,
          countryCode: address.countryCode,
          postalCode: address.postalCode
        },
        idNumber: user.idDocument?.value, // Use first passport/ID if available
        nationality: user.nationalities?.[0],
        residencyCountry: user.residencyCountry,
        email: user.email
      };

      if (platformData.platform === "android" || platformData.platform === "ios") {
        platformData.deviceToken = user.deviceTokens[platformData.platform];
      }

      // Create the acceptance record
      const acceptanceData: TermsAcceptanceDTOInterface = {
        owner: user._id,
        userData,
        platformData,
        termsVersion
      };

      const termsAcceptance = new TermsAcceptance(acceptanceData);
      termsAcceptance.acceptanceId = hashSHA256(
        JSON.stringify({
          userId: user._id,
          termsVersion,
          createdAt: new Date(Date.now()).getTime()
        })
      );
      await termsAcceptance.save();

      logger.info("Terms acceptance recorded", {
        module: "TermsAcceptanceService",
        method: "recordTermsAcceptance",
        data: {
          userId: user._id,
          email: user.email,
          termsVersion,
          acceptanceId: termsAcceptance.acceptanceId
        }
      });
    } catch (error) {
      logger.error("Failed to record terms acceptance", {
        module: "TermsAcceptanceService",
        method: "recordTermsAcceptance",
        data: {
          userId: user._id,
          email: user.email,
          termsVersion,
          error: error.message
        }
      });
      throw new InternalServerError("Failed to record terms acceptance");
    }
  }
}
