import { entitiesConfig } from "@wealthyhood/shared-configs";
import logger from "../external-services/loggerService";
import { InternalServerError } from "../models/ApiErrors";
import { TermsVersion, TermsVersionDocument, TermsVersionDTOInterface } from "../models/TermsVersion";

const CURRENT_TERMS_VERSION_UK = "2024.9.1";
const CURRENT_TERMS_VERSION_EU = "2025.6.1";

export const CURRENT_TERMS_VERSION: Record<entitiesConfig.CompanyEntityEnum, string> = {
  [entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK]: CURRENT_TERMS_VERSION_UK,
  [entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE]: CURRENT_TERMS_VERSION_EU
};

export default class TermsVersionService {
  /**
   * Creates a new terms verison record if it does not already exist, otherwise updates the existing one
   * @param termsVersionData The terms version data
   * @returns The created terms version document
   */
  public static async createOrUpdateTermsVersion(
    termsVersionData: TermsVersionDTOInterface
  ): Promise<TermsVersionDocument> {
    try {
      const termsVersion = await TermsVersion.findOneAndUpdate(
        { termsVersion: termsVersionData.termsVersion, companyEntity: termsVersionData.companyEntity },
        termsVersionData,
        {
          runValidators: true,
          setDefaultsOnInsert: true,
          upsert: true,
          new: true
        }
      );

      logger.info("Terms version created/updated", {
        module: "TermsVersionService",
        method: "createOrUpdateTermsVersion",
        data: {
          id: termsVersion._id,
          termsVersion: termsVersion.termsVersion,
          companyEntity: termsVersion.companyEntity
        }
      });

      return termsVersion;
    } catch (error) {
      logger.error("Failed to create/updated terms version", {
        module: "TermsVersionService",
        method: "createOrUpdateTermsVersion",
        data: {
          error: error,
          termsRevisionData: termsVersionData
        }
      });
      throw new InternalServerError("Failed to create terms version");
    }
  }

  /**
   * Gets the latest terms version for a company entity
   * @param companyEntity The company entity
   * @returns The latest terms version document or null if none found
   */
  public static async getLatestTermsVersion(
    companyEntity: entitiesConfig.CompanyEntityEnum
  ): Promise<TermsVersionDocument | null> {
    return TermsVersion.findOne({ companyEntity }).sort({ createdAt: -1 });
  }

  /**
   * Gets the current active terms version for a company entity
   * This is a helper method that returns the version string from the latest terms version
   * @param companyEntity The company entity
   * @returns The current terms version string
   */
  public static async getCurrentTermsVersion(companyEntity: entitiesConfig.CompanyEntityEnum): Promise<string> {
    const latestTermsRevision = await this.getLatestTermsVersion(companyEntity);

    if (!latestTermsRevision) {
      // Fallback to hardcoded versions if no terms versions are recorded
      const currentVersion = CURRENT_TERMS_VERSION[companyEntity];

      logger.warn("No terms versions found, using fallback version", {
        module: "TermsVersionService",
        method: "getCurrentTermsVersion",
        data: {
          companyEntity,
          currentVersion: currentVersion
        }
      });

      return currentVersion;
    }

    return latestTermsRevision.termsVersion;
  }
}
