import { clearDb, closeDb, connectDb } from "../../../tests/utils/db";
import request from "supertest";
import app from "../../../app";
import { buildAddress, buildUser } from "../../../tests/utils/generateModels";
import { User, UserDocument } from "../../../models/User";
import { TermsAcceptance } from "../../../models/TermsAcceptance";
import { entitiesConfig } from "@wealthyhood/shared-configs";
import logger from "../../../external-services/loggerService";

describe("POST /users/me/terms-acceptance", () => {
  beforeAll(async () => {
    await connectDb("post-me-terms-acceptance");
  });
  afterAll(async () => {
    await closeDb();
  });
  afterEach(async () => await clearDb());

  describe("when user does not have an address", () => {
    let user: UserDocument;

    beforeEach(async () => {
      user = await buildUser({
        hasAcceptedTerms: false,
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      // Don't create an address for this user
    });

    it("should return 400 and not create a terms acceptance record", async () => {
      const response = await request(app)
        .post("/api/m2m/users/me/terms-acceptance")
        .set("external-user-id", user.id)
        .set("Accept", "application/json")
        .set("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)")
        .set("X-Forwarded-For", "***********");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Operation failed",
            message: "User must have an address to accept terms"
          }
        })
      );

      // User should not have accepted terms
      const updatedUser = await User.findById(user.id);
      expect(updatedUser?.hasAcceptedTerms).toBe(false);

      // No terms acceptance record should be created
      const termsAcceptanceRecords = await TermsAcceptance.find({ owner: user.id });
      expect(termsAcceptanceRecords.length).toBe(0);

      // Should log an error
      expect(logger.error).toHaveBeenCalledWith(
        expect.stringContaining("does not have an address, cannot record terms acceptance"),
        expect.objectContaining({
          module: "TermsAcceptanceService",
          method: "recordTermsAcceptance"
        })
      );
    });
  });

  describe("when user has an address", () => {
    let user: UserDocument;

    beforeEach(async () => {
      user = await buildUser({
        hasAcceptedTerms: false,
        companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
      });
      await buildAddress({ owner: user.id });
    });

    it("should record terms acceptance and return 204", async () => {
      const response = await request(app)
        .post("/api/m2m/users/me/terms-acceptance")
        .set("external-user-id", user.id)
        .set("Accept", "application/json")
        .set("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)")
        .set("X-Forwarded-For", "***********");

      expect(response.status).toEqual(204);

      // Check that terms acceptance was recorded
      const updatedUser = await User.findById(user.id);
      expect(updatedUser?.hasAcceptedTerms).toBe(true);

      // Check that a TermsAcceptance record was created
      const termsAcceptanceRecords = await TermsAcceptance.find({ owner: user.id });
      expect(termsAcceptanceRecords.length).toBe(1);
      expect(termsAcceptanceRecords[0].userData.email).toBe(user.email);
      expect(termsAcceptanceRecords[0].platformData.ip).toBe("***********");
    });

    it("should not create duplicate terms acceptance records for the same user", async () => {
      // First request
      await request(app)
        .post("/api/m2m/users/me/terms-acceptance")
        .set("external-user-id", user.id)
        .set("Accept", "application/json")
        .set("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)")
        .set("X-Forwarded-For", "***********");

      // Second request
      await request(app)
        .post("/api/m2m/users/me/terms-acceptance")
        .set("external-user-id", user.id)
        .set("Accept", "application/json")
        .set("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)")
        .set("X-Forwarded-For", "***********");

      // Should only have one terms acceptance record
      const termsAcceptanceRecords = await TermsAcceptance.find({ owner: user.id });
      expect(termsAcceptanceRecords.length).toBe(1);
    });

    it("should extract technical context from request headers", async () => {
      const response = await request(app)
        .post("/api/m2m/users/me/terms-acceptance")
        .set("external-user-id", user.id)
        .set("Accept", "application/json")
        .set("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36")
        .set("X-Forwarded-For", "***********");

      expect(response.status).toEqual(204);

      const termsAcceptanceRecord = await TermsAcceptance.findOne({ owner: user.id });
      expect(termsAcceptanceRecord?.platformData.ip).toBe("***********");
      expect(termsAcceptanceRecord?.platformData.browserUserAgent).toBe(
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"
      );
      expect(termsAcceptanceRecord?.platformData.platform).toBe("web");
    });
  });
});
