import { CustomRequest } from "custom";
import { PlatformDataInterface } from "../models/TermsAcceptance";

export default class RequestContextUtil {
  /**
   * Extracts platform data from the HTTP request
   * @param req The HTTP request object
   * @param user The user document for device token info
   * @returns Platform data object
   * @private
   */
  public static extractPlatformData(req: CustomRequest): PlatformDataInterface {
    // Extract IP address (considering proxy headers)
    const forwardedIp = req.headers["x-forwarded-for"] as string;
    const publicIP = forwardedIp || req.ip;

    // Extract platform from headers or request
    const platform = req.platform || "web";

    // Extract user agent
    const browserUserAgent = req.headers["user-agent"] as string;

    // Extract device/OS information
    const deviceOS = (req.headers["x-device-os"] as string) || (req.headers["device-os"] as string);

    // Extract mobile app build info
    const mobileAppBuild =
      (req.headers["x-app-version"] as string) ||
      (req.headers["app-version"] as string) ||
      (req.headers["x-build-version"] as string);

    return {
      ip: Array.isArray(publicIP) ? publicIP[0] : publicIP,
      deviceOS,
      browserUserAgent,
      mobileAppBuild,
      platform
    };
  }
}
