import "./dependencies";
import ScriptRunner from "../jobs/services/scriptRunner";
import UserService from "../services/userService";
import { AssetTransaction } from "../models/Transaction";
import PortfolioService from "../services/portfolioService";

class TestScriptRunner extends ScriptRunner {
  scriptName = "debug";

  alexId = "6865316ed2a2adc69bc48b58";
  portfolioId = "6865316e5f8a6c069319f897";

  async processFn(): Promise<void> {
    await PortfolioService.getPricesByTenor(this.portfolioId, "EUR");
  }
}

new TestScriptRunner().run();
