# Terms Management Scripts

This directory contains scripts for managing terms and conditions changes, including scraping terms snapshots and storing change information.

## Scripts

### 1. <PERSON><PERSON> Scraper (`terms-scraper.ts`)

Scrapes the Wealthyhood terms and conditions page, downloads all assets (CSS, JS, images), and creates a ZIP snapshot that is uploaded to Cloudflare R2.

#### Usage

```bash
npm run terms:scrape
```

#### What it does

1. Downloads the main terms page HTML from `https://wealthyhood.com/terms-and-conditions`
2. Parses the HTML to extract all linked assets (CSS, JavaScript, images)
3. Downloads all assets
4. Updates HTML to use local asset paths
5. Creates a ZIP file containing:
   - `index.html` - The main terms page with updated asset paths
   - `assets/` - Directory containing all downloaded assets
   - `metadata.json` - Information about the scraping process
6. Uploads the ZIP to Cloudflare R2 bucket `terms-snapshots`

#### Output

The script will output the ZIP file name and URL where it was uploaded.

### 2. Terms Revision Storage (`store-terms-revision.ts`)

Stores information about terms revisions in the database, including version, update time, notification time, and snapshot URL.

#### Usage

```bash
npm run terms:store-revision <termsVersion> <companyEntity> [options]
```

#### Parameters

- `termsVersion` - The version of the terms (format: YYYY.M.D, e.g., "2025.1.1")
- `companyEntity` - The company entity ("WEALTHYHOOD_UK" or "WEALTHYHOOD_EUROPE")

#### Options

- `--snapshot-url <url>` - URL of the terms snapshot ZIP file
- `--description <text>` - Description of the changes made

#### Examples

```bash
# Basic usage
npm run terms:store-revision 2025.1.1 WEALTHYHOOD_UK

# With snapshot URL and description
npm run terms:store-revision 2025.1.1 WEALTHYHOOD_UK --snapshot-url "https://terms-snapshots.wealthyhood.cloud/terms-snapshot-2025-01-01T10-00-00-000Z.zip" --description "Updated privacy policy section"

# With user notification
npm run terms:store-revision 2025.1.1 WEALTHYHOOD_UK --description "Major terms update - please review"

```

#### What it does

1. Validates the input parameters
2. Creates or updates a terms revision record in the database

## Typical Workflow

When updating terms and conditions:

1. **First, scrape the current terms:**
   ```bash
   npm run terms:scrape
   ```
   This creates a snapshot of the current terms page.

2. **Store the terms revision information:**
   ```bash
   npm run terms:store-revision 2025.1.1 WEALTHYHOOD_UK --snapshot-url "https://terms-snapshots.wealthyhood.cloud/terms-snapshot-2025-01-01T10-00-00-000Z.zip" --description "Updated data processing clauses"
   ```
   This records the revision and notifies users.

## Database Schema

The `TermsRevision` model stores:

- `termsVersion` - Version string (e.g., "2025.1.1")
- `companyEntity` - Company entity enum
- `notifiedAt` - When users were notified (optional)
- `snapshotUrl` - URL to the terms snapshot ZIP file (optional)
- `description` - Description of the changes (optional)
- `createdAt` - When the record was created
- `updatedAt` - When the record was last updated

## Cloudflare Integration

Terms snapshots are stored in the `terms-snapshots` bucket in Cloudflare R2. The bucket URL is configured in the CloudflareService.

## Testing

Tests are available for the new functionality:

```bash
# Run all tests
npm test

# Run specific test files
npm test -- src/models/__tests__/TermsRevision.test.ts
npm test -- src/services/__tests__/TermsVersionService.test.ts
```

## Error Handling

Both scripts include comprehensive error handling and logging. Check the application logs for detailed information about any failures.

## Dependencies

The scripts use:

- `jsdom` - For HTML parsing
- `adm-zip` - For creating ZIP files
- `axios` - For HTTP requests
- Cloudflare R2 - For file storage
- MongoDB - For data persistence
