import <PERSON>riptRunner from "../../jobs/services/scriptRunner";
import { TermsVersion, TermsVersionDTOInterface } from "../../models/TermsVersion";
import { entitiesConfig } from "@wealthyhood/shared-configs";
import logger from "../../external-services/loggerService";
import TermsVersionService from "../../services/termsVersionService";

interface TermsRevisionOptions {
  termsVersion: string;
  companyEntity: entitiesConfig.CompanyEntityEnum;
  snapshotUrl: string;
  notified?: boolean;
}

class StoreTermsRevisionScriptRunner extends ScriptRunner {
  scriptName = "store-terms-revision";
  private options: TermsRevisionOptions;

  constructor(options: TermsRevisionOptions) {
    super();
    this.options = options;
  }

  async processFn(): Promise<void> {
    logger.info("Starting terms revision storage...", {
      module: `script:${this.scriptName}`,
      data: this.options
    });

    try {
      // Validate input
      this.validateOptions();

      // Check if this terms version already exists
      const existingTermsRevision = await TermsVersion.findOne({
        termsVersion: this.options.termsVersion,
        companyEntity: this.options.companyEntity
      });

      if (existingTermsRevision) {
        logger.info("Terms revision already exists, updating...", {
          module: `script:${this.scriptName}`,
          data: {
            existingId: existingTermsRevision._id,
            termsVersion: this.options.termsVersion,
            companyEntity: this.options.companyEntity
          }
        });
      }

      const termsRevisionData: TermsVersionDTOInterface = {
        termsVersion: this.options.termsVersion,
        companyEntity: this.options.companyEntity,
        snapshotUrl: this.options.snapshotUrl,
        notifiedAt: this.options.notified ? new Date() : undefined
      };

      const termsRevision = await TermsVersionService.createOrUpdateTermsVersion(termsRevisionData);

      logger.info("Terms revision record saved", {
        module: `script:${this.scriptName}`,
        data: {
          id: termsRevision._id,
          termsVersion: termsRevision.termsVersion,
          companyEntity: termsRevision.companyEntity
        }
      });

      console.log(`\nTerms revision stored successfully!`);
      console.log(`ID: ${termsRevision._id}`);
      console.log(`Terms Version: ${termsRevision.termsVersion}`);
      console.log(`Company Entity: ${termsRevision.companyEntity}`);
      console.log(`Snapshot URL: ${termsRevision.snapshotUrl}`);
      if (termsRevision.notifiedAt) {
        console.log(`Users Notified At: ${termsRevision.notifiedAt}`);
      }
    } catch (error) {
      logger.error("Terms revision storage failed", {
        module: `script:${this.scriptName}`,
        data: { error: error.message, options: this.options }
      });
      throw error;
    }
  }

  private validateOptions(): void {
    if (!this.options.termsVersion) {
      throw new Error("Terms version is required");
    }

    if (!Object.values(entitiesConfig.CompanyEntityEnum).includes(this.options.companyEntity)) {
      throw new Error(`Invalid company entity: ${this.options.companyEntity}`);
    }

    // Validate terms version format (should be like "2024.9.1" or "2025.7.1")
    const versionPattern = /^\d{4}\.\d{1,2}\.\d{1,2}$/;
    if (!versionPattern.test(this.options.termsVersion)) {
      throw new Error(`Invalid terms version format: ${this.options.termsVersion}. Expected format: YYYY.M.D`);
    }
  }
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);

  if (args.length < 3) {
    console.error(
      "Usage: ts-node store-terms-revision.ts <termsVersion> <companyEntity> <snapshotUrl> --notified"
    );
    console.error(
      "Example: ts-node store-terms-revision.ts 2025.1.1 WEALTHYHOOD_UK https://example.com/snapshot.zip --notified"
    );
    process.exit(1);
  }

  const termsVersion = args[0];
  const companyEntity = args[1] as entitiesConfig.CompanyEntityEnum;
  const snapshotUrl = args[2];

  const notified = args.includes("--notified");

  const options: TermsRevisionOptions = {
    termsVersion,
    companyEntity,
    snapshotUrl,
    notified
  };

  new StoreTermsRevisionScriptRunner(options).run();
}
