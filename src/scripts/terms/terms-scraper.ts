import <PERSON>riptRunner from "../../jobs/services/scriptRunner";
import axios from "axios";
import { JSDOM } from "jsdom";
import Adm<PERSON><PERSON> from "adm-zip";
import path from "path";
import { URL } from "url";
import CloudflareService, { BucketsEnum, ContentTypeEnum } from "../../external-services/cloudflareService";
import logger from "../../external-services/loggerService";
import { Readable } from "stream";

interface ScrapedAsset {
  url: string;
  localPath: string;
  content: Buffer;
}

interface TermsScraperOptions {
  termsUrl: string;
}

class TermsScraperScriptRunner extends ScriptRunner {
  scriptName = "terms-scraper";

  private options: TermsScraperOptions;

  constructor(options: TermsScraperOptions) {
    super();
    this.options = options;
  }
  async processFn(): Promise<void> {
    logger.info("Starting terms scraping...", {
      module: `script:${this.scriptName}`,
      data: this.options
    });

    try {
      const termsUrl = this.options.termsUrl;

      if (!termsUrl) {
        console.error("Terms URL is required");
        logger.error("Terms URL is required", {
          module: `script:${this.scriptName}`
        });
        throw new Error("Terms URL is required");
      }

      // Step 1: Download the main HTML page
      logger.info(`Downloading terms page: ${termsUrl}`, {
        module: `script:${this.scriptName}`
      });

      const htmlResponse = await axios.get(termsUrl);
      const htmlContent = htmlResponse.data;

      // Step 2: Parse HTML and extract asset URLs
      logger.info("Parsing HTML and extracting assets...", {
        module: `script:${this.scriptName}`
      });

      const dom = new JSDOM(htmlContent);
      const document = dom.window.document;

      const assets = await this._extractAssets(document, termsUrl);

      // Step 3: Download all assets
      logger.info(`Downloading ${assets.length} assets...`, {
        module: `script:${this.scriptName}`
      });

      const downloadedAssets = await this._downloadAssets(assets);

      // Step 4: Update HTML to use local asset paths and remove Gatsby loader
      const updatedHtml = this._updateHtmlAssetPathsAndRemoveGatsbyLoader(htmlContent, downloadedAssets);

      // Step 5: Create ZIP file
      logger.info("Creating ZIP file...", {
        module: `script:${this.scriptName}`
      });

      const zipBuffer = this._createZipFile(updatedHtml, downloadedAssets);

      // Step 6: Upload to Cloudflare
      const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
      const zipFileName = `terms-snapshot-${timestamp}.zip`;

      logger.info(`Uploading ZIP file to Cloudflare: ${zipFileName}`, {
        module: `script:${this.scriptName}`
      });

      const zipStream = Readable.from(zipBuffer);
      const uploadResult = await CloudflareService.Instance.uploadObject(
        BucketsEnum.TERMS_SNAPSHOTS,
        zipFileName,
        zipStream,
        { contentType: ContentTypeEnum.APPLICATION_ZIP }
      );

      logger.info("Terms scraping completed successfully", {
        module: `script:${this.scriptName}`,
        data: {
          zipFileName,
          fileUri: uploadResult.fileUri,
          assetsCount: downloadedAssets.length
        }
      });

      console.log(`\nTerms snapshot created successfully!`);
      console.log(`File: ${zipFileName}`);
      console.log(`URL: ${uploadResult.fileUri}`);
      console.log(`Assets downloaded: ${downloadedAssets.length}`);
    } catch (error) {
      logger.error("Terms scraping failed", {
        module: `script:${this.scriptName}`,
        data: { error: error.message }
      });
      throw error;
    }
  }

  private async _extractAssets(document: Document, baseUrl: string): Promise<ScrapedAsset[]> {
    const assets: ScrapedAsset[] = [];
    const baseUrlObj = new URL(baseUrl);

    // Extract CSS files
    const linkElements = document.querySelectorAll('link[rel="stylesheet"]');
    linkElements.forEach((link) => {
      const href = link.getAttribute("href");
      if (href) {
        const assetUrl = this._resolveUrl(href, baseUrlObj);
        if (assetUrl) {
          assets.push({
            url: assetUrl,
            localPath: `assets/css/${path.basename(new URL(assetUrl).pathname)}`,
            content: Buffer.alloc(0)
          });
        }
      }
    });

    // Extract JavaScript files
    const scriptElements = document.querySelectorAll("script[src]");
    scriptElements.forEach((script) => {
      const src = script.getAttribute("src");
      if (src) {
        const assetUrl = this._resolveUrl(src, baseUrlObj);
        if (assetUrl) {
          assets.push({
            url: assetUrl,
            localPath: `assets/js/${path.basename(new URL(assetUrl).pathname)}`,
            content: Buffer.alloc(0)
          });
        }
      }
    });

    // Extract images
    const imgElements = document.querySelectorAll("img[src]");
    imgElements.forEach((img) => {
      const src = img.getAttribute("src");
      if (src) {
        const assetUrl = this._resolveUrl(src, baseUrlObj);
        if (assetUrl) {
          const urlObj = new URL(assetUrl);
          const ext = path.extname(urlObj.pathname) || ".jpg";
          assets.push({
            url: assetUrl,
            localPath: `assets/images/${path.basename(urlObj.pathname) || "image" + ext}`,
            content: Buffer.alloc(0)
          });
        }
      }
    });

    // Extract pdfs
    const pdfElements = document.querySelectorAll("a[href$='.pdf']");
    pdfElements.forEach((pdf) => {
      const href = pdf.getAttribute("href");
      if (href) {
        const assetUrl = this._resolveUrl(href, baseUrlObj);
        if (assetUrl) {
          assets.push({
            url: assetUrl,
            localPath: `assets/pdf/${path.basename(new URL(assetUrl).pathname)}`,
            content: Buffer.alloc(0)
          });
        }
      }
    });

    return assets;
  }

  private _resolveUrl(url: string, baseUrl: URL): string | null {
    try {
      if (url.startsWith("http://") || url.startsWith("https://")) {
        return url;
      }
      if (url.startsWith("//")) {
        return `${baseUrl.protocol}${url}`;
      }
      if (url.startsWith("/")) {
        return `${baseUrl.protocol}//${baseUrl.host}${url}`;
      }
      return `${baseUrl.protocol}//${baseUrl.host}${baseUrl.pathname}/${url}`;
    } catch (error) {
      logger.warn(`Failed to resolve URL: ${url}`, {
        module: `script:${this.scriptName}`,
        data: { error: error.message }
      });
      return null;
    }
  }

  private async _downloadAssets(assets: ScrapedAsset[]): Promise<ScrapedAsset[]> {
    const downloadedAssets: ScrapedAsset[] = [];

    for (const asset of assets) {
      try {
        logger.info(`Downloading asset: ${asset.url}`, {
          module: `script:${this.scriptName}`
        });

        const response = await axios.get(asset.url, {
          responseType: "arraybuffer",
          timeout: 30000
        });

        downloadedAssets.push({
          ...asset,
          content: Buffer.from(response.data)
        });
      } catch (error) {
        logger.warn(`Failed to download asset: ${asset.url}`, {
          module: `script:${this.scriptName}`,
          data: { error: error.message }
        });
        // Continue with other assets even if one fails
      }
    }

    return downloadedAssets;
  }

  private _updateHtmlAssetPathsAndRemoveGatsbyLoader(htmlContent: string, assets: ScrapedAsset[]): string {
    let updatedHtml = htmlContent;

    // 1) Remove the Gatsby loader script by id from the HTML string
    // Matches: <script ... id="gatsby-script-loader" ...> ... </script> (any order of attributes)
    updatedHtml = updatedHtml.replace(
      /<script[^>]*\bid=["']gatsby-script-loader["'][^>]*>[\s\S]*?<\/script>/i,
      ""
    );

    assets.forEach((asset) => {
      // Replace absolute URLs with relative paths
      const urlObj = new URL(asset.url);
      const patterns = [asset.url, urlObj.pathname, urlObj.href];

      patterns.forEach((pattern) => {
        updatedHtml = updatedHtml.replace(
          new RegExp(pattern.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"), "g"),
          asset.localPath
        );
      });
    });

    return updatedHtml;
  }

  private _createZipFile(htmlContent: string, assets: ScrapedAsset[]): Buffer {
    const zip = new AdmZip();

    // Add the main HTML file
    zip.addFile("index.html", Buffer.from(htmlContent, "utf8"));

    // Add all assets
    assets.forEach((asset) => {
      if (asset.content.length > 0) {
        zip.addFile(asset.localPath, asset.content);
      }
    });

    // Add a metadata file
    const metadata = {
      scrapedAt: new Date().toISOString(),
      sourceUrl: this.options.termsUrl,
      assetsCount: assets.length,
      assets: assets.map((a) => ({ url: a.url, localPath: a.localPath, size: a.content.length }))
    };

    zip.addFile("metadata.json", Buffer.from(JSON.stringify(metadata, null, 2), "utf8"));

    return zip.toBuffer();
  }
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);

  if (args.length < 1) {
    console.error("Usage: ts-node terms-scraper.js <termsUrl>");
    console.error("Example: ts-node terms-scraper.ts ");

    process.exit(1);
  }

  const termsUrl = args[0];

  const options: TermsScraperOptions = {
    termsUrl: termsUrl
  };

  new TermsScraperScriptRunner(options).run();
}
