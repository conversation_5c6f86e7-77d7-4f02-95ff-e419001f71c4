import mongoose, { Document, Schema } from "mongoose";
import { countriesConfig } from "@wealthyhood/shared-configs";

/**
 * ENUMS
 */
export const PlatformArray = ["ios", "android", "web"] as const;
export type PlatformType = (typeof PlatformArray)[number];

/**
 * INTERFACES
 */
export interface UserDataInterface {
  fullName: string;
  dateOfBirth: Date;
  address: {
    line1: string;
    line2?: string;
    line3?: string;
    city: string;
    region?: string;
    countryCode: countriesConfig.CountryCodesType;
    postalCode: string;
  };
  idNumber?: string; // ID or passport number
  nationality: countriesConfig.CountryCodesType;
  residencyCountry: countriesConfig.CountryCodesType;
  email: string;
  phone?: string;
}

export interface PlatformDataInterface {
  ip: string;
  deviceOS?: string;
  browserUserAgent?: string;
  mobileAppBuild?: string;
  deviceToken?: string;
  platform: PlatformType;
}

export interface TermsAcceptanceDTOInterface {
  owner: mongoose.Types.ObjectId;
  userData: UserDataInterface;
  platformData: PlatformDataInterface;
  termsVersion: string;
}

export interface TermsAcceptanceInterface extends TermsAcceptanceDTOInterface {
  acceptanceId: string; // Hash of the full acceptance record
  createdAt: Date;
  updatedAt: Date;
}

export interface TermsAcceptanceDocument extends TermsAcceptanceInterface, Document {}

/**
 * SCHEMA
 */
const userDataSchema = new mongoose.Schema(
  {
    fullName: { type: String, required: true },
    dateOfBirth: { type: Date, required: true },
    address: {
      _id: false,
      line1: { type: String, required: true },
      line2: String,
      line3: String,
      city: { type: String, required: true },
      region: String,
      countryCode: {
        type: String,
        enum: countriesConfig.countryCodesArray,
        required: true
      },
      postalCode: { type: String, required: true }
    },
    idNumber: { type: String },
    nationality: {
      type: String,
      enum: countriesConfig.countryCodesArray,
      required: true
    },
    residencyCountry: {
      type: String,
      enum: countriesConfig.countryCodesArray,
      required: true
    },
    email: { type: String, required: true, lowercase: true },
    phone: String
  },
  { _id: false }
);

const platformDataSchema = new mongoose.Schema(
  {
    ip: { type: String, required: true },
    deviceOS: String,
    browserUserAgent: String,
    mobileAppBuild: String,
    deviceToken: String,
    platform: {
      type: String,
      enum: PlatformArray,
      required: true
    }
  },
  { _id: false }
);

const termsAcceptanceSchema: Schema = new mongoose.Schema(
  {
    owner: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
      index: true
    },
    userData: {
      type: userDataSchema,
      required: true
    },
    platformData: {
      type: platformDataSchema,
      required: true
    },
    termsVersion: {
      type: String,
      required: true,
      index: true
    },
    acceptanceId: {
      type: String,
      required: true,
      unique: true,
      index: true
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

/**
 * INDEXES
 */
termsAcceptanceSchema.index({ owner: 1, termsVersion: 1 });

export const TermsAcceptance = mongoose.model<TermsAcceptanceDocument>("TermsAcceptance", termsAcceptanceSchema);
