import mongoose, { Document, Schema } from "mongoose";
import { entitiesConfig } from "@wealthyhood/shared-configs";

/**
 * INTERFACES
 */
export interface TermsVersionDTOInterface {
  termsVersion: string;
  companyEntity: entitiesConfig.CompanyEntityEnum;
  notifiedAt?: Date;
  snapshotUrl?: string;
  description?: string;
  createdAt?: Date;
}

export interface TermsVersionInterface extends TermsVersionDTOInterface {
  createdAt: Date;
  updatedAt: Date;
}

export interface TermsVersionDocument extends TermsVersionInterface, Document {}

/**
 * SCHEMA
 */
const termsVersionSchema: Schema = new mongoose.Schema(
  {
    termsVersion: {
      type: String,
      required: true,
      index: true
    },
    companyEntity: {
      type: String,
      enum: Object.values(entitiesConfig.CompanyEntityEnum),
      required: true,
      index: true
    },
    notifiedAt: {
      type: Date,
      index: true
    },
    snapshotUrl: {
      type: String
    },
    description: {
      type: String
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

/**
 * INDEXES
 */
termsVersionSchema.index({ companyEntity: 1, termsVersion: 1 }, { unique: true });
termsVersionSchema.index({ companyEntity: 1, createdAt: -1 });

export const TermsVersion = mongoose.model<TermsVersionDocument>("TermsVersion", termsVersionSchema);
